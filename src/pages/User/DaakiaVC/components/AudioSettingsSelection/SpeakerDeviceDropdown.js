import React, { useCallback, useMemo } from "react";
import { Dropdown } from "antd";
import { useMediaDeviceSelect } from "@livekit/components-react";
import { ReactComponent as SpeakerIcon } from "../settings/icons/SpeakerIcon.svg";
import "./SpeakerDeviceDropdown.scss";

const SpeakerDeviceDropdown = React.memo(function SpeakerDeviceDropdown({
  kind = "audiooutput",
  onActiveDeviceChange = undefined,
  initialSelection = undefined,
  className = ""
}) {
  const { devices, activeDeviceId, setActiveMediaDevice } = useMediaDeviceSelect({ kind });

  const displayDevice = useMemo(() => {
    return initialSelection || activeDeviceId;
  }, [initialSelection, activeDeviceId]);

  // Note: Removed automatic device setting to prevent interference with audio system
  // The device should already be set correctly by LiveKit, and forcing it here
  // causes unnecessary device changes that can interrupt audio processing

  const handleDeviceChange = useCallback(async (deviceId) => {
    try {
      await setActiveMediaDevice(deviceId);
      onActiveDeviceChange?.(deviceId);
    } catch (error) {
      console.error('Failed to switch speaker device:', error);
    }
  }, [setActiveMediaDevice, onActiveDeviceChange]);

  const dropdownContent = useMemo(() => (
    <div className="speaker-device-dropdown-menu">
      {devices.map((device) => {
        const isSelected = displayDevice === device.deviceId;
        const deviceLabel = device.label || `${kind} ${device.deviceId.slice(0, 8)}...`;

        return (
          <button
            key={device.deviceId}
            className={`device-option ${isSelected ? 'selected' : ''}`}
            onClick={() => handleDeviceChange(device.deviceId)}
            aria-label={`Select ${deviceLabel}`}
            type="button"
          >
            {deviceLabel}
          </button>
        );
      })}
    </div>
  ), [devices, displayDevice, kind, handleDeviceChange]);

  const currentDeviceName = useMemo(() => {
    const currentDevice = devices.find(device => device.deviceId === displayDevice);
    return currentDevice?.label || 'Default Speaker';
  }, [devices, displayDevice]);

  return (
    <Dropdown
      dropdownRender={() => dropdownContent}
      trigger={['click']}
      placement="top"
      className={className}
      disabled={devices.length === 0}
    >
      <button
        className="speaker-device-dropdown-button"
        type="button"
        aria-label={`Current speaker device: ${currentDeviceName}. Click to change device.`}
        aria-haspopup="listbox"
        aria-expanded="false"
      >
        <SpeakerIcon className="device-icon" />
        <span className="device-name">{currentDeviceName}</span>
        <span className="dropdown-arrow">▼</span>
      </button>
    </Dropdown>
  );
});





export default SpeakerDeviceDropdown;
