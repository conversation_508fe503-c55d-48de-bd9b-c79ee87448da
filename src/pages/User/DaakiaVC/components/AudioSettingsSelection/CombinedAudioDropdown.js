import React, { useEffect, useRef } from "react";
import { Dropdown, Switch } from "antd";
import AudioDeviceDropdown from "./AudioDeviceDropdown";
import SpeakerDeviceDropdown from "./SpeakerDeviceDropdown";
import { useNoiseSuppressionContext } from "../../context/indexContext";
import "./CombinedAudioDropdown.scss";

const CombinedAudioDropdown = React.memo(function CombinedAudioDropdown({
  micSelection = undefined,
  speakerSelection = undefined,
  onMicChange = undefined,
  onSpeakerChange = undefined,
  className = ""
}) {
  // Get noise suppression state from context
  const { isNoiseSuppressionEnabled, setIsNoiseSuppressionEnabled } = useNoiseSuppressionContext();

  // Track if we've set initial devices (only run once for initial setup)
  const hasSetInitialDevices = useRef(false);

  // Apply initial device settings (ONLY ONCE for initial setup)
  useEffect(() => {
    if (hasSetInitialDevices.current) return; // Only run once

    const applyInitialDevices = async () => {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();

        // Apply mic device if provided
        if (micSelection) {
          const micExists = devices.some(device => device.kind === 'audioinput' && device.deviceId === micSelection);
          if (micExists && onMicChange) {
            onMicChange(micSelection);
          }
        }

        // Apply speaker device if provided
        if (speakerSelection) {
          const speakerExists = devices.some(device => device.kind === 'audiooutput' && device.deviceId === speakerSelection);
          if (speakerExists && onSpeakerChange) {
            onSpeakerChange(speakerSelection);
          }
        }

        hasSetInitialDevices.current = true;
      } catch (error) {
        console.error('Error applying initial devices:', error);
      }
    };

    if (micSelection || speakerSelection) {
      applyInitialDevices();
    }
  }, [micSelection, speakerSelection, onMicChange, onSpeakerChange]);
  const dropdownContent = (
    <div className="combined-audio-dropdown-menu">
      <div className="audio-settings-row">
        {/* Microphone Section */}
        <div className="audio-setting-section">
          <AudioDeviceDropdown
            kind="audioinput"
            onActiveDeviceChange={onMicChange}
            initialSelection={micSelection}
            className="device-dropdown"
          />
        </div>
        <div className="audio-setting-section">
          <SpeakerDeviceDropdown
            kind="audiooutput"
            onActiveDeviceChange={onSpeakerChange}
            initialSelection={speakerSelection}
            className="device-dropdown"
          />
        </div>
      </div>

      {/* Noise Suppression Section */}
      <div className="noise-suppression-section">
        <div className="noise-suppression-label">
          <span>Noise Suppression</span>
          <small>Reduce background noise during calls</small>
        </div>
        <Switch
          checked={isNoiseSuppressionEnabled}
          onChange={(enabled) => {
            console.log('🎛️ Noise suppression toggled:', enabled);
            setIsNoiseSuppressionEnabled(enabled);
          }}
          size="default"
        />
      </div>
    </div>
  );

  return (
    <Dropdown
      dropdownRender={() => dropdownContent}
      trigger={['click']}
      placement="top"
      className={className}
    >
      <button
        className="combined-audio-dropdown-button"
        type="button"
        aria-label="Audio settings - microphone and speaker selection"
        aria-haspopup="listbox"
        aria-expanded="false"
      >
        <span>▼</span>
      </button>
    </Dropdown>
  );
});





export default CombinedAudioDropdown;
