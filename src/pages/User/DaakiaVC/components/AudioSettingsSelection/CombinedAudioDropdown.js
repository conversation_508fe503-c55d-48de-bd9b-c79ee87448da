import React, { useEffect, useRef } from "react";
import { Dropdown, Switch } from "antd";
import { useMediaDeviceSelect } from "@livekit/components-react";
import AudioDeviceDropdown from "./AudioDeviceDropdown";
import SpeakerDeviceDropdown from "./SpeakerDeviceDropdown";
import { useNoiseSuppressionContext } from "../../context/indexContext";
import "./CombinedAudioDropdown.scss";

const CombinedAudioDropdown = React.memo(function CombinedAudioDropdown({
  micSelection = undefined,
  speakerSelection = undefined,
  onMicChange = undefined,
  onSpeakerChange = undefined,
  className = ""
}) {
  // Get noise suppression state from context
  const { isNoiseSuppressionEnabled, setIsNoiseSuppressionEnabled } = useNoiseSuppressionContext();

  // Get LiveKit device management hooks
  const { devices: audioInputDevices, activeDeviceId: activeMicId, setActiveMediaDevice: setActiveMicDevice } = useMediaDeviceSelect({ kind: "audioinput" });
  const { devices: audioOutputDevices, activeDeviceId: activeSpeakerId, setActiveMediaDevice: setActiveSpeakerDevice } = useMediaDeviceSelect({ kind: "audiooutput" });

  // Apply microphone device if different from current
  useEffect(() => {
    if (micSelection && micSelection !== activeMicId && audioInputDevices.length > 0) {
      const deviceExists = audioInputDevices.some(device => device.deviceId === micSelection);
      if (deviceExists) {
        setActiveMicDevice(micSelection).catch(error => {
          console.error('Failed to set mic device:', error);
        });
      }
    }
  }, [micSelection, activeMicId, setActiveMicDevice, audioInputDevices]);

  // Apply speaker device if different from current
  useEffect(() => {
    if (speakerSelection && speakerSelection !== activeSpeakerId && audioOutputDevices.length > 0) {
      const deviceExists = audioOutputDevices.some(device => device.deviceId === speakerSelection);
      if (deviceExists) {
        setActiveSpeakerDevice(speakerSelection).catch(error => {
          console.error('Failed to set speaker device:', error);
        });
      }
    }
  }, [speakerSelection, activeSpeakerId, setActiveSpeakerDevice, audioOutputDevices]);
  const dropdownContent = (
    <div className="combined-audio-dropdown-menu">
      <div className="audio-settings-row">
        {/* Microphone Section */}
        <div className="audio-setting-section">
          <AudioDeviceDropdown
            kind="audioinput"
            onActiveDeviceChange={onMicChange}
            initialSelection={micSelection}
            className="device-dropdown"
          />
        </div>
        <div className="audio-setting-section">
          <SpeakerDeviceDropdown
            kind="audiooutput"
            onActiveDeviceChange={onSpeakerChange}
            initialSelection={speakerSelection}
            className="device-dropdown"
          />
        </div>
      </div>

      {/* Noise Suppression Section */}
      <div className="noise-suppression-section">
        <div className="noise-suppression-label">
          <span>Noise Suppression</span>
          <small>Reduce background noise during calls</small>
        </div>
        <Switch
          checked={isNoiseSuppressionEnabled}
          onChange={(enabled) => {
            console.log('🎛️ Noise suppression toggled:', enabled);
            setIsNoiseSuppressionEnabled(enabled);
          }}
          size="default"
        />
      </div>
    </div>
  );

  return (
    <Dropdown
      dropdownRender={() => dropdownContent}
      trigger={['click']}
      placement="top"
      className={className}
    >
      <button
        className="combined-audio-dropdown-button"
        type="button"
        aria-label="Audio settings - microphone and speaker selection"
        aria-haspopup="listbox"
        aria-expanded="false"
      >
        <span>▼</span>
      </button>
    </Dropdown>
  );
});





export default CombinedAudioDropdown;
