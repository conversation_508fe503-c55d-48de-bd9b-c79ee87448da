import React, { useEffect, useRef } from "react";
import { Dropdown, Switch } from "antd";
import { useMediaDeviceSelect } from "@livekit/components-react";
import AudioDeviceDropdown from "./AudioDeviceDropdown";
import SpeakerDeviceDropdown from "./SpeakerDeviceDropdown";
import { useNoiseSuppressionContext } from "../../context/indexContext";
import "./CombinedAudioDropdown.scss";

const CombinedAudioDropdown = React.memo(function CombinedAudioDropdown({
  micSelection = undefined,
  speakerSelection = undefined,
  onMicChange = undefined,
  onSpeakerChange = undefined,
  className = ""
}) {
  // Get noise suppression state from context
  const { isNoiseSuppressionEnabled, setIsNoiseSuppressionEnabled } = useNoiseSuppressionContext();

  // Get LiveKit device management hooks
  const { devices: audioInputDevices, setActiveMediaDevice: setActiveMicDevice } = useMediaDeviceSelect({ kind: "audioinput" });
  const { devices: audioOutputDevices, setActiveMediaDevice: setActiveSpeakerDevice } = useMediaDeviceSelect({ kind: "audiooutput" });

  // Track if we've already applied initial device settings (separate for each device type)
  const hasAppliedInitialMic = useRef(false);
  const hasAppliedInitialSpeaker = useRef(false);

  // Apply initial microphone device when component first mounts (when joining room)
  useEffect(() => {
    if (hasAppliedInitialMic.current) return;

    const applyInitialMicDevice = async () => {
      try {
        if (micSelection && audioInputDevices.length > 0) {
          const micExists = audioInputDevices.some(device => device.deviceId === micSelection);
          if (micExists) {
            console.log('🎤 CombinedAudioDropdown: Applying initial mic device:', micSelection);
            await setActiveMicDevice(micSelection);
            hasAppliedInitialMic.current = true;
          }
        }
      } catch (error) {
        console.error('Error applying initial mic device:', error);
      }
    };

    // Only apply if we have mic selection and devices available
    if (micSelection && audioInputDevices.length > 0) {
      applyInitialMicDevice();
    }
  }, [micSelection, audioInputDevices, setActiveMicDevice]);

  // Apply initial speaker device when component first mounts (when joining room)
  useEffect(() => {
    if (hasAppliedInitialSpeaker.current) return;

    const applyInitialSpeakerDevice = async () => {
      try {
        if (speakerSelection && audioOutputDevices.length > 0) {
          const speakerExists = audioOutputDevices.some(device => device.deviceId === speakerSelection);
          if (speakerExists) {
            console.log('🔊 CombinedAudioDropdown: Applying initial speaker device:', speakerSelection);
            await setActiveSpeakerDevice(speakerSelection);
            hasAppliedInitialSpeaker.current = true;
          }
        }
      } catch (error) {
        console.error('Error applying initial speaker device:', error);
      }
    };

    // Only apply if we have speaker selection and devices available
    if (speakerSelection && audioOutputDevices.length > 0) {
      applyInitialSpeakerDevice();
    }
  }, [speakerSelection, audioOutputDevices, setActiveSpeakerDevice]);
  const dropdownContent = (
    <div className="combined-audio-dropdown-menu">
      <div className="audio-settings-row">
        {/* Microphone Section */}
        <div className="audio-setting-section">
          <AudioDeviceDropdown
            kind="audioinput"
            onActiveDeviceChange={onMicChange}
            initialSelection={micSelection}
            className="device-dropdown"
          />
        </div>
        <div className="audio-setting-section">
          <SpeakerDeviceDropdown
            kind="audiooutput"
            onActiveDeviceChange={onSpeakerChange}
            initialSelection={speakerSelection}
            className="device-dropdown"
          />
        </div>
      </div>

      {/* Noise Suppression Section */}
      <div className="noise-suppression-section">
        <div className="noise-suppression-label">
          <span>Noise Suppression</span>
          <small>Reduce background noise during calls</small>
        </div>
        <Switch
          checked={isNoiseSuppressionEnabled}
          onChange={(enabled) => {
            console.log('🎛️ Noise suppression toggled:', enabled);
            setIsNoiseSuppressionEnabled(enabled);
          }}
          size="default"
        />
      </div>
    </div>
  );

  return (
    <Dropdown
      dropdownRender={() => dropdownContent}
      trigger={['click']}
      placement="top"
      className={className}
    >
      <button
        className="combined-audio-dropdown-button"
        type="button"
        aria-label="Audio settings - microphone and speaker selection"
        aria-haspopup="listbox"
        aria-expanded="false"
      >
        <span>▼</span>
      </button>
    </Dropdown>
  );
});





export default CombinedAudioDropdown;
