import { useEffect, useRef } from 'react';
import { Track } from 'livekit-client';
import { NoiseSuppressionProcessor } from '@shiguredo/noise-suppression';
import { useNoiseSuppressionContext } from '../context/indexContext';

/**
 * Custom hook for managing noise suppression on microphone audio
 * @param {Object} room - LiveKit room instance
 * @param {string} deviceIdAudio - Current audio device ID
 * @returns {Object} - Noise suppression state and controls
 */
export const useNoiseSuppression = (room, deviceIdAudio) => {
  const { isNoiseSuppressionEnabled } = useNoiseSuppressionContext();
  const noiseProcessor = useRef(null);
  const originalTrack = useRef(null);

  // Cleanup function to stop noise suppression
  const stopNoiseSuppressionAsync = async () => {
    try {
      if (noiseProcessor.current) {
        console.log('🔇 Stopping noise suppression...');
        
        // Get current mic publication
        const micPublication = room?.localParticipant?.getTrackPublication(Track.Source.Microphone);
        
        if (micPublication?.track && originalTrack.current) {
          // Restore original track
          await micPublication.track.replaceTrack(originalTrack.current, true);
          console.log('✅ Original track restored');
        }
        
        // Clean up processor
        noiseProcessor.current.stopProcessing();
        noiseProcessor.current = null;
        originalTrack.current = null;
        console.log('🧹 Noise processor cleaned up');
      }
    } catch (error) {
      console.error('❌ Error stopping noise suppression:', error);
    }
  };

  // Main noise suppression effect
  useEffect(() => {
    if (!room?.localParticipant) return;

    const micPublication = room.localParticipant.getTrackPublication(Track.Source.Microphone);
    const isEnabled = micPublication?.isEnabled;
    const hasTrack = !!micPublication?.track;

    // Console log all mic states
    console.log('🎤 Microphone State:', {
      isEnabled,
      hasTrack,
      isMuted: micPublication?.isMuted,
      isPublished: micPublication?.track && !micPublication?.isMuted,
      isNoiseSuppressionEnabled,
      hasNoiseProcessor: !!noiseProcessor.current,
      hasOriginalTrack: !!originalTrack.current,
      deviceIdAudio,
      trackId: micPublication?.track?.sid,
      participantIdentity: room.localParticipant.identity
    });

    const startNoiseSuppressionAsync = async () => {
      try {
        // Case 1: NS is enabled but no mic track yet - just log and wait
        if (isNoiseSuppressionEnabled && !hasTrack) {
          console.log('🎛️ Noise suppression enabled, waiting for microphone...');
          return;
        }

        // Case 1.5: NS is enabled, mic exists but is muted - wait for unmute
        if (isNoiseSuppressionEnabled && hasTrack && micPublication?.isMuted) {
          console.log('🔇 Noise suppression enabled, waiting for microphone to be unmuted...');
          return;
        }

        // Case 2: Everything ready - apply noise suppression with delay
        const isPublished = micPublication?.track && !micPublication?.isMuted;
        if (isEnabled && hasTrack && isPublished && isNoiseSuppressionEnabled && !noiseProcessor.current) {
          console.log('🔊 Noise suppression requested - letting mic work normally first...');
          const localAudioTrack = micPublication.track;

          // Wait for mic to work normally first (1 second delay)
          setTimeout(async () => {
            try {
              // Double check conditions are still valid after delay
              const currentMicPublication = room?.localParticipant?.getTrackPublication(Track.Source.Microphone);
              const stillEnabled = currentMicPublication?.isEnabled;
              const stillHasTrack = !!currentMicPublication?.track;
              const stillPublished = currentMicPublication?.track && !currentMicPublication?.isMuted;

              if (stillEnabled && stillHasTrack && stillPublished && isNoiseSuppressionEnabled && !noiseProcessor.current) {
                console.log('🔊 Now applying noise suppression after delay...');

                // Store original track
                originalTrack.current = localAudioTrack.mediaStreamTrack;
                console.log('💾 Original track stored');

                // Create and start noise processor
                noiseProcessor.current = new NoiseSuppressionProcessor();
                const processedTrack = await noiseProcessor.current.startProcessing(
                  localAudioTrack.mediaStreamTrack
                );

                if (processedTrack) {
                  await localAudioTrack.replaceTrack(processedTrack, true);
                  console.log('✅ Noise suppression applied successfully after delay');
                }
              } else {
                console.log('⏹️ Conditions changed during delay, skipping noise suppression');
              }
            } catch (error) {
              console.error('❌ Delayed noise suppression error:', error);
            }
          }, 1000); // 1 second delay to let mic work normally first

        } else if ((!isEnabled || !hasTrack || !isPublished || !isNoiseSuppressionEnabled) && noiseProcessor.current) {
          // Stop noise suppression when mic is off, no track, or toggle is disabled
          console.log('🛑 Conditions changed, stopping noise suppression...');
          await stopNoiseSuppressionAsync();
        }
      } catch (error) {
        console.error('❌ Noise suppression error:', error);
      }
    };

    startNoiseSuppressionAsync();
  }, [
    room?.localParticipant?.getTrackPublication(Track.Source.Microphone)?.isEnabled,
    room?.localParticipant?.getTrackPublication(Track.Source.Microphone)?.track,
    room?.localParticipant?.getTrackPublication(Track.Source.Microphone)?.isMuted, // Re-apply when mute state changes
    // Removed deviceIdAudio dependency to prevent unnecessary restarts when device changes
    isNoiseSuppressionEnabled // Re-apply when toggle changes
  ]);

  // Handle device changes separately to avoid unnecessary noise suppression restarts
  useEffect(() => {
    if (!room?.localParticipant || !isNoiseSuppressionEnabled) return;

    const micPublication = room.localParticipant.getTrackPublication(Track.Source.Microphone);
    const isEnabled = micPublication?.isEnabled;
    const hasTrack = !!micPublication?.track;
    const isPublished = micPublication?.track && !micPublication?.isMuted;

    // Only handle device changes when noise suppression should be active
    if (isEnabled && hasTrack && isPublished && noiseProcessor.current) {
      console.log('🔄 Device changed while noise suppression active, recreating processor for new track...');

      const handleDeviceChange = async () => {
        try {
          const localAudioTrack = micPublication.track;

          // Stop the current processor first
          if (noiseProcessor.current) {
            noiseProcessor.current.stopProcessing();
            noiseProcessor.current = null;
            console.log('🛑 Stopped old noise processor for device change');
          }

          // Store the new original track
          originalTrack.current = localAudioTrack.mediaStreamTrack;
          console.log('💾 New original track stored for device change');

          // Create new processor for the new track
          noiseProcessor.current = new NoiseSuppressionProcessor();
          const processedTrack = await noiseProcessor.current.startProcessing(
            localAudioTrack.mediaStreamTrack
          );

          if (processedTrack) {
            await localAudioTrack.replaceTrack(processedTrack, true);
            console.log('✅ Noise suppression recreated and applied to new device');
          }
        } catch (error) {
          console.error('❌ Error recreating noise suppression for new device:', error);
          // Clean up on error
          if (noiseProcessor.current) {
            noiseProcessor.current.stopProcessing();
            noiseProcessor.current = null;
          }
          originalTrack.current = null;
        }
      };

      // Small delay to let the device change settle
      const timeoutId = setTimeout(handleDeviceChange, 500);

      return () => clearTimeout(timeoutId);
    }
  }, [deviceIdAudio]); // Only watch device changes

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      console.log('🧹 Component cleanup - stopping noise suppression');
      stopNoiseSuppressionAsync();
    };
  }, []);

  return {
    isNoiseSuppressionActive: !!noiseProcessor.current,
    hasOriginalTrack: !!originalTrack.current,
    stopNoiseSuppression: stopNoiseSuppressionAsync
  };
};
